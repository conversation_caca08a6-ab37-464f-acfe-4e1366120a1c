# Milestone M0 Technical Reference

**Quick reference for the technical implementation details of M0**

---

## 📦 Package Versions (Final Implementation)

```yaml
# Core Runtime
node: "20.11.0"
pnpm: "8.15.4"

# TypeScript Ecosystem
typescript: "5.4.3"
tsup: "8.0.2"
ts-jest: "29.1.2"

# Build & Development
turbo: "1.13.2"
vite: "5.2.2"
nodemon: "3.1.0"

# Testing
jest: "29.7.0"
vitest: "1.5.0"
supertest: "7.1.1"  # Updated from 6.4.2

# Code Quality
eslint: "8.56.0"
prettier: "3.2.5"
eslint-config-prettier: "9.1.0"  # Added
husky: "9.0.11"
lint-staged: "15.2.2"

# Frontend
react: "18.2.0"
react-dom: "18.2.0"
@testing-library/react: "14.3.1"
@testing-library/jest-dom: "6.4.5"
jsdom: "24.1.0"
```

---

## 🏗 Architecture Overview

### Monorepo Structure
```
workflow-mapper/
├── apps/
│   ├── api/              # Express TypeScript API
│   │   ├── src/
│   │   │   ├── index.ts  # Main server file
│   │   │   └── index.test.ts
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   └── jest.config.js
│   └── web/              # React + Vite SPA
│       ├── src/
│       │   ├── main.tsx
│       │   ├── App.tsx
│       │   ├── App.test.tsx
│       │   └── test-setup.ts
│       ├── package.json
│       ├── tsconfig.json
│       ├── tsconfig.node.json
│       ├── vite.config.ts
│       └── index.html
├── packages/
│   └── shared/           # Shared utilities
│       ├── src/
│       │   ├── index.ts
│       │   ├── Result.ts
│       │   └── Result.test.ts
│       ├── package.json
│       └── tsconfig.json
├── scripts/
│   └── m0-acceptance.sh  # Comprehensive acceptance tests
├── .github/workflows/
│   └── ci.yml           # GitHub Actions pipeline
├── work-log/
│   └── milestone-m0/    # Implementation documentation
├── docs/
│   └── tech-specs/      # Technical specifications
└── [config files]      # Root configuration
```

### Key Configuration Files
- `pnpm-workspace.yaml` - Workspace definition
- `turbo.json` - Build pipeline configuration
- `tsconfig.json` - Root TypeScript config
- `.eslintrc.cjs` - ESLint configuration
- `.prettierrc` - Prettier formatting rules
- `.lintstagedrc.json` - Pre-commit hook configuration
- `Dockerfile` - Container configuration
- `docker-compose.yml` - Local development stack

---

## 🔧 Development Commands

### Root Level Commands
```bash
# Development
pnpm dev:api          # Start API server on :3000
pnpm dev:web          # Start web app on :5173

# Quality Assurance
pnpm lint             # ESLint all files
pnpm type-check       # TypeScript validation
pnpm test             # Run all tests
pnpm build            # Build all packages

# Acceptance Testing
bash scripts/m0-acceptance.sh  # Full validation
```

### Package-Specific Commands
```bash
# API (apps/api)
pnpm dev              # nodemon development server
pnpm build            # tsup build to dist/
pnpm start            # node dist/index.js
pnpm test             # jest unit tests

# Web (apps/web)
pnpm dev              # vite development server
pnpm build            # vite build to dist/
pnpm preview          # preview built app
pnpm test             # vitest unit tests

# Shared (packages/shared)
pnpm build            # tsup with .d.ts generation
pnpm test             # vitest unit tests
```

---

## 🧪 Testing Strategy

### API Testing (Jest + Supertest)
- **Framework**: Jest with ts-jest for TypeScript
- **HTTP Testing**: Supertest for endpoint testing
- **Location**: `apps/api/src/*.test.ts`
- **Config**: `apps/api/jest.config.js`

### Web Testing (Vitest + Testing Library)
- **Framework**: Vitest for fast testing
- **Component Testing**: React Testing Library
- **DOM Environment**: jsdom
- **Location**: `apps/web/src/*.test.tsx`
- **Config**: `apps/web/vite.config.ts` (test section)

### Shared Testing (Vitest)
- **Framework**: Vitest for consistency
- **Type Testing**: TypeScript type validation
- **Location**: `packages/shared/src/*.test.ts`

---

## 🚀 CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - uses: actions/setup-node@v4
        with: { node-version: 20.11.0, cache: pnpm }
      - run: corepack enable
      - run: pnpm install --frozen-lockfile
      - run: pnpm lint
      - run: pnpm test --recursive
      - run: pnpm build
      - run: bash scripts/m0-acceptance.sh
```

### Pipeline Stages
1. **Setup**: Node.js 20.11.0 + pnpm 8.15.4
2. **Install**: Frozen lockfile for reproducibility
3. **Lint**: ESLint validation
4. **Test**: All package tests in parallel
5. **Build**: All packages build successfully
6. **Acceptance**: Comprehensive validation script

---

## 🐳 Docker Configuration

### Development Stack
```yaml
# docker-compose.yml
version: '3.9'
services:
  api:
    build: .
    ports: [ "3000:3000" ]
    depends_on: [ neo4j ]
  neo4j:
    image: neo4j:5
    environment:
      - NEO4J_AUTH=neo4j/test
    ports: [ "7474:7474" ]
```

### Production Dockerfile
```dockerfile
FROM node:20.11.0-alpine
WORKDIR /app

# Copy package files for better caching
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY apps/api/package.json ./apps/api/
COPY apps/web/package.json ./apps/web/
COPY packages/shared/package.json ./packages/shared/

# Install dependencies
RUN corepack enable && pnpm install --frozen-lockfile

# Copy source and build
COPY . .
RUN pnpm build

# Start API server
EXPOSE 3000
CMD ["node", "apps/api/dist/index.js"]
```

---

## 🔍 Health Endpoints

### API Health Endpoint
- **URL**: `http://localhost:3000/health`
- **Response**: `{"status":"ok"}`
- **Implementation**: Direct Express route

### Web Health Endpoint
- **URL**: `http://localhost:5173/health`
- **Response**: Proxied to API
- **Implementation**: Vite proxy configuration

```typescript
// apps/web/vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      '/health': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  }
});
```

---

## 🔧 Known Issues & Workarounds

### Lint-staged Global Config Conflict
- **Issue**: Global lint-staged configs interfere with project config
- **Workaround**: Use `git commit --no-verify` for initial commits
- **Resolution**: Clear global configs or use project-specific settings

### Docker Build on ARM64
- **Issue**: Some packages may have ARM64 compatibility issues
- **Workaround**: Multi-stage builds or platform-specific images
- **Status**: Documented for future optimization

---

## 📊 Performance Benchmarks

### Build Performance
- **Full build**: ~1.6s (target: ≤60s) ✅
- **Incremental build**: <1s with Turbo cache
- **Test execution**: ~2.5s for all packages
- **Type checking**: ~1.4s across all packages

### Development Experience
- **Hot reload**: <100ms for web changes
- **API restart**: <2s with nodemon
- **Lint feedback**: Real-time in IDE
- **Test feedback**: Watch mode available

---

**Reference Updated**: 2025-05-25
**Implementation Status**: ✅ Complete
**Next Milestone**: M1 - Static Graph Builder
