hoistPattern:
  - '*'
hoistedDependencies:
  /@adobe/css-tools/4.4.3:
    '@adobe/css-tools': private
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@asamuzakjp/css-color/3.2.0:
    '@asamuzakjp/css-color': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.27.2:
    '@babel/compat-data': private
  /@babel/core/7.27.1:
    '@babel/core': private
  /@babel/generator/7.27.1:
    '@babel/generator': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.1(@babel/core@7.27.1):
    '@babel/helper-module-transforms': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helpers/7.27.1:
    '@babel/helpers': private
  /@babel/parser/7.27.2:
    '@babel/parser': private
  /@babel/plugin-syntax-async-generators/7.8.4(@babel/core@7.27.1):
    '@babel/plugin-syntax-async-generators': private
  /@babel/plugin-syntax-bigint/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-bigint': private
  /@babel/plugin-syntax-class-properties/7.12.13(@babel/core@7.27.1):
    '@babel/plugin-syntax-class-properties': private
  /@babel/plugin-syntax-class-static-block/7.14.5(@babel/core@7.27.1):
    '@babel/plugin-syntax-class-static-block': private
  /@babel/plugin-syntax-import-attributes/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-syntax-import-attributes': private
  /@babel/plugin-syntax-import-meta/7.10.4(@babel/core@7.27.1):
    '@babel/plugin-syntax-import-meta': private
  /@babel/plugin-syntax-json-strings/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-json-strings': private
  /@babel/plugin-syntax-jsx/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-syntax-jsx': private
  /@babel/plugin-syntax-logical-assignment-operators/7.10.4(@babel/core@7.27.1):
    '@babel/plugin-syntax-logical-assignment-operators': private
  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  /@babel/plugin-syntax-numeric-separator/7.10.4(@babel/core@7.27.1):
    '@babel/plugin-syntax-numeric-separator': private
  /@babel/plugin-syntax-object-rest-spread/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-object-rest-spread': private
  /@babel/plugin-syntax-optional-catch-binding/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-optional-catch-binding': private
  /@babel/plugin-syntax-optional-chaining/7.8.3(@babel/core@7.27.1):
    '@babel/plugin-syntax-optional-chaining': private
  /@babel/plugin-syntax-private-property-in-object/7.14.5(@babel/core@7.27.1):
    '@babel/plugin-syntax-private-property-in-object': private
  /@babel/plugin-syntax-top-level-await/7.14.5(@babel/core@7.27.1):
    '@babel/plugin-syntax-top-level-await': private
  /@babel/plugin-syntax-typescript/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-syntax-typescript': private
  /@babel/plugin-transform-react-jsx-self/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-react-jsx-self': private
  /@babel/plugin-transform-react-jsx-source/7.27.1(@babel/core@7.27.1):
    '@babel/plugin-transform-react-jsx-source': private
  /@babel/runtime/7.27.1:
    '@babel/runtime': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.27.1:
    '@babel/traverse': private
  /@babel/types/7.27.1:
    '@babel/types': private
  /@bcoe/v8-coverage/0.2.3:
    '@bcoe/v8-coverage': private
  /@csstools/color-helpers/5.0.2:
    '@csstools/color-helpers': private
  /@csstools/css-calc/2.1.3(@csstools/css-parser-algorithms@3.0.4)(@csstools/css-tokenizer@3.0.3):
    '@csstools/css-calc': private
  /@csstools/css-color-parser/3.0.9(@csstools/css-parser-algorithms@3.0.4)(@csstools/css-tokenizer@3.0.3):
    '@csstools/css-color-parser': private
  /@csstools/css-parser-algorithms/3.0.4(@csstools/css-tokenizer@3.0.3):
    '@csstools/css-parser-algorithms': private
  /@csstools/css-tokenizer/3.0.3:
    '@csstools/css-tokenizer': private
  /@esbuild/aix-ppc64/0.19.12:
    '@esbuild/aix-ppc64': private
  /@esbuild/android-arm/0.19.12:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.19.12:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.19.12:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.19.12:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.19.12:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.19.12:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.19.12:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.19.12:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.19.12:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.19.12:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.19.12:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.19.12:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.19.12:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.19.12:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.19.12:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.19.12:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-x64/0.19.12:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-x64/0.19.12:
    '@esbuild/openbsd-x64': private
  /@esbuild/sunos-x64/0.19.12:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.19.12:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.19.12:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.19.12:
    '@esbuild/win32-x64': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.56.0):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.56.0:
    '@eslint/js': public
  /@humanwhocodes/config-array/0.11.14:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@istanbuljs/load-nyc-config/1.1.0:
    '@istanbuljs/load-nyc-config': private
  /@istanbuljs/schema/0.1.3:
    '@istanbuljs/schema': private
  /@jest/console/29.7.0:
    '@jest/console': private
  /@jest/core/29.7.0:
    '@jest/core': private
  /@jest/environment/29.7.0:
    '@jest/environment': private
  /@jest/expect-utils/29.7.0:
    '@jest/expect-utils': private
  /@jest/expect/29.7.0:
    '@jest/expect': private
  /@jest/fake-timers/29.7.0:
    '@jest/fake-timers': private
  /@jest/globals/29.7.0:
    '@jest/globals': private
  /@jest/reporters/29.7.0:
    '@jest/reporters': private
  /@jest/schemas/29.6.3:
    '@jest/schemas': private
  /@jest/source-map/29.6.3:
    '@jest/source-map': private
  /@jest/test-result/29.7.0:
    '@jest/test-result': private
  /@jest/test-sequencer/29.7.0:
    '@jest/test-sequencer': private
  /@jest/transform/29.7.0:
    '@jest/transform': private
  /@jest/types/29.6.3:
    '@jest/types': private
  /@jridgewell/gen-mapping/0.3.8:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/set-array/1.2.1:
    '@jridgewell/set-array': private
  /@jridgewell/sourcemap-codec/1.5.0:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.25:
    '@jridgewell/trace-mapping': private
  /@noble/hashes/1.8.0:
    '@noble/hashes': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@paralleldrive/cuid2/2.2.2:
    '@paralleldrive/cuid2': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@pkgr/core/0.1.2:
    '@pkgr/core': private
  /@rollup/rollup-android-arm-eabi/4.41.1:
    '@rollup/rollup-android-arm-eabi': private
  /@rollup/rollup-android-arm64/4.41.1:
    '@rollup/rollup-android-arm64': private
  /@rollup/rollup-darwin-arm64/4.41.1:
    '@rollup/rollup-darwin-arm64': private
  /@rollup/rollup-darwin-x64/4.41.1:
    '@rollup/rollup-darwin-x64': private
  /@rollup/rollup-freebsd-arm64/4.41.1:
    '@rollup/rollup-freebsd-arm64': private
  /@rollup/rollup-freebsd-x64/4.41.1:
    '@rollup/rollup-freebsd-x64': private
  /@rollup/rollup-linux-arm-gnueabihf/4.41.1:
    '@rollup/rollup-linux-arm-gnueabihf': private
  /@rollup/rollup-linux-arm-musleabihf/4.41.1:
    '@rollup/rollup-linux-arm-musleabihf': private
  /@rollup/rollup-linux-arm64-gnu/4.41.1:
    '@rollup/rollup-linux-arm64-gnu': private
  /@rollup/rollup-linux-arm64-musl/4.41.1:
    '@rollup/rollup-linux-arm64-musl': private
  /@rollup/rollup-linux-loongarch64-gnu/4.41.1:
    '@rollup/rollup-linux-loongarch64-gnu': private
  /@rollup/rollup-linux-powerpc64le-gnu/4.41.1:
    '@rollup/rollup-linux-powerpc64le-gnu': private
  /@rollup/rollup-linux-riscv64-gnu/4.41.1:
    '@rollup/rollup-linux-riscv64-gnu': private
  /@rollup/rollup-linux-riscv64-musl/4.41.1:
    '@rollup/rollup-linux-riscv64-musl': private
  /@rollup/rollup-linux-s390x-gnu/4.41.1:
    '@rollup/rollup-linux-s390x-gnu': private
  /@rollup/rollup-linux-x64-gnu/4.41.1:
    '@rollup/rollup-linux-x64-gnu': private
  /@rollup/rollup-linux-x64-musl/4.41.1:
    '@rollup/rollup-linux-x64-musl': private
  /@rollup/rollup-win32-arm64-msvc/4.41.1:
    '@rollup/rollup-win32-arm64-msvc': private
  /@rollup/rollup-win32-ia32-msvc/4.41.1:
    '@rollup/rollup-win32-ia32-msvc': private
  /@rollup/rollup-win32-x64-msvc/4.41.1:
    '@rollup/rollup-win32-x64-msvc': private
  /@sinclair/typebox/0.27.8:
    '@sinclair/typebox': private
  /@sinonjs/commons/3.0.1:
    '@sinonjs/commons': private
  /@sinonjs/fake-timers/10.3.0:
    '@sinonjs/fake-timers': private
  /@testing-library/dom/9.3.4:
    '@testing-library/dom': private
  /@testing-library/jest-dom/6.4.5(@types/jest@29.5.12)(jest@29.7.0)(vitest@1.5.0):
    '@testing-library/jest-dom': private
  /@testing-library/react/14.3.1(react-dom@18.2.0)(react@18.2.0):
    '@testing-library/react': private
  /@types/aria-query/5.0.4:
    '@types/aria-query': private
  /@types/babel__core/7.20.5:
    '@types/babel__core': private
  /@types/babel__generator/7.27.0:
    '@types/babel__generator': private
  /@types/babel__template/7.4.4:
    '@types/babel__template': private
  /@types/babel__traverse/7.20.7:
    '@types/babel__traverse': private
  /@types/body-parser/1.19.5:
    '@types/body-parser': private
  /@types/connect/3.4.38:
    '@types/connect': private
  /@types/cookiejar/2.1.5:
    '@types/cookiejar': private
  /@types/estree/1.0.7:
    '@types/estree': private
  /@types/express-serve-static-core/4.19.6:
    '@types/express-serve-static-core': private
  /@types/express/4.17.21:
    '@types/express': private
  /@types/graceful-fs/4.1.9:
    '@types/graceful-fs': private
  /@types/http-errors/2.0.4:
    '@types/http-errors': private
  /@types/istanbul-lib-coverage/2.0.6:
    '@types/istanbul-lib-coverage': private
  /@types/istanbul-lib-report/3.0.3:
    '@types/istanbul-lib-report': private
  /@types/istanbul-reports/3.0.4:
    '@types/istanbul-reports': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/methods/1.1.4:
    '@types/methods': private
  /@types/mime/1.3.5:
    '@types/mime': private
  /@types/prop-types/15.7.14:
    '@types/prop-types': private
  /@types/qs/6.14.0:
    '@types/qs': private
  /@types/range-parser/1.2.7:
    '@types/range-parser': private
  /@types/react-dom/18.2.25:
    '@types/react-dom': private
  /@types/react/18.2.79:
    '@types/react': private
  /@types/semver/7.7.0:
    '@types/semver': private
  /@types/send/0.17.4:
    '@types/send': private
  /@types/serve-static/1.15.7:
    '@types/serve-static': private
  /@types/stack-utils/2.0.3:
    '@types/stack-utils': private
  /@types/superagent/8.1.9:
    '@types/superagent': private
  /@types/yargs-parser/21.0.3:
    '@types/yargs-parser': private
  /@types/yargs/17.0.33:
    '@types/yargs': private
  /@typescript-eslint/scope-manager/7.7.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/type-utils/7.7.0(eslint@8.56.0)(typescript@5.4.3):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/7.7.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/7.7.0(typescript@5.4.3):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/7.7.0(eslint@8.56.0)(typescript@5.4.3):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/7.7.0:
    '@typescript-eslint/visitor-keys': public
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /@vitejs/plugin-react/4.2.1(vite@5.2.2):
    '@vitejs/plugin-react': private
  /@vitest/expect/1.5.0:
    '@vitest/expect': private
  /@vitest/runner/1.5.0:
    '@vitest/runner': private
  /@vitest/snapshot/1.5.0:
    '@vitest/snapshot': private
  /@vitest/spy/1.5.0:
    '@vitest/spy': private
  /@vitest/utils/1.5.0:
    '@vitest/utils': private
  /accepts/1.3.8:
    accepts: private
  /acorn-jsx/5.3.2(acorn@8.14.1):
    acorn-jsx: private
  /acorn-walk/8.3.4:
    acorn-walk: private
  /acorn/8.14.1:
    acorn: private
  /agent-base/7.1.3:
    agent-base: private
  /ajv/6.12.6:
    ajv: private
  /ansi-escapes/4.3.2:
    ansi-escapes: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /argparse/2.0.1:
    argparse: private
  /aria-query/5.3.2:
    aria-query: private
  /array-buffer-byte-length/1.0.2:
    array-buffer-byte-length: private
  /array-flatten/1.1.1:
    array-flatten: private
  /array-union/2.1.0:
    array-union: private
  /asap/2.0.6:
    asap: private
  /assertion-error/1.1.0:
    assertion-error: private
  /asynckit/0.4.0:
    asynckit: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /babel-jest/29.7.0(@babel/core@7.27.1):
    babel-jest: private
  /babel-plugin-istanbul/6.1.1:
    babel-plugin-istanbul: private
  /babel-plugin-jest-hoist/29.6.3:
    babel-plugin-jest-hoist: private
  /babel-preset-current-node-syntax/1.1.0(@babel/core@7.27.1):
    babel-preset-current-node-syntax: private
  /babel-preset-jest/29.6.3(@babel/core@7.27.1):
    babel-preset-jest: private
  /balanced-match/1.0.2:
    balanced-match: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /body-parser/1.20.2:
    body-parser: private
  /brace-expansion/1.1.11:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.24.5:
    browserslist: private
  /bs-logger/0.2.6:
    bs-logger: private
  /bser/2.1.1:
    bser: private
  /buffer-from/1.1.2:
    buffer-from: private
  /bundle-require/4.2.1(esbuild@0.19.12):
    bundle-require: private
  /bytes/3.1.2:
    bytes: private
  /cac/6.7.14:
    cac: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camelcase/6.3.0:
    camelcase: private
  /caniuse-lite/1.0.30001718:
    caniuse-lite: private
  /chai/4.5.0:
    chai: private
  /chalk/3.0.0:
    chalk: private
  /char-regex/1.0.2:
    char-regex: private
  /check-error/1.0.3:
    check-error: private
  /chokidar/3.6.0:
    chokidar: private
  /ci-info/3.9.0:
    ci-info: private
  /cjs-module-lexer/1.4.3:
    cjs-module-lexer: private
  /cli-cursor/5.0.0:
    cli-cursor: private
  /cli-truncate/4.0.0:
    cli-truncate: private
  /cliui/8.0.1:
    cliui: private
  /co/4.6.0:
    co: private
  /collect-v8-coverage/1.0.2:
    collect-v8-coverage: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /colorette/2.0.20:
    colorette: private
  /combined-stream/1.0.8:
    combined-stream: private
  /commander/11.1.0:
    commander: private
  /component-emitter/1.3.1:
    component-emitter: private
  /concat-map/0.0.1:
    concat-map: private
  /confbox/0.1.8:
    confbox: private
  /content-disposition/0.5.4:
    content-disposition: private
  /content-type/1.0.5:
    content-type: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cookie-signature/1.0.6:
    cookie-signature: private
  /cookie/0.6.0:
    cookie: private
  /cookiejar/2.1.4:
    cookiejar: private
  /create-jest/29.7.0(@types/node@20.12.7):
    create-jest: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /css.escape/1.5.1:
    css.escape: private
  /cssstyle/4.3.1:
    cssstyle: private
  /csstype/3.1.3:
    csstype: private
  /data-urls/5.0.0:
    data-urls: private
  /debug/4.4.1(supports-color@5.5.0):
    debug: private
  /decimal.js/10.5.0:
    decimal.js: private
  /dedent/1.6.0:
    dedent: private
  /deep-eql/4.1.4:
    deep-eql: private
  /deep-equal/2.2.3:
    deep-equal: private
  /deep-is/0.1.4:
    deep-is: private
  /deepmerge/4.3.1:
    deepmerge: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-properties/1.2.1:
    define-properties: private
  /delayed-stream/1.0.0:
    delayed-stream: private
  /depd/2.0.0:
    depd: private
  /destroy/1.2.0:
    destroy: private
  /detect-newline/3.1.0:
    detect-newline: private
  /dezalgo/1.0.4:
    dezalgo: private
  /diff-sequences/29.6.3:
    diff-sequences: private
  /dir-glob/3.0.1:
    dir-glob: private
  /doctrine/3.0.0:
    doctrine: private
  /dom-accessibility-api/0.6.3:
    dom-accessibility-api: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /ee-first/1.1.1:
    ee-first: private
  /electron-to-chromium/1.5.157:
    electron-to-chromium: private
  /emittery/0.13.1:
    emittery: private
  /emoji-regex/8.0.0:
    emoji-regex: private
  /encodeurl/1.0.2:
    encodeurl: private
  /entities/6.0.0:
    entities: private
  /environment/1.1.0:
    environment: private
  /error-ex/1.3.2:
    error-ex: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-get-iterator/1.1.3:
    es-get-iterator: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /esbuild/0.19.12:
    esbuild: private
  /escalade/3.2.0:
    escalade: private
  /escape-html/1.0.3:
    escape-html: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /espree/9.6.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /estree-walker/3.0.3:
    estree-walker: private
  /esutils/2.0.3:
    esutils: private
  /etag/1.8.1:
    etag: private
  /eventemitter3/5.0.1:
    eventemitter3: private
  /execa/8.0.1:
    execa: private
  /exit/0.1.2:
    exit: private
  /expect/29.7.0:
    expect: private
  /express/4.19.2:
    express: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-diff/1.3.0:
    fast-diff: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fast-safe-stringify/2.1.1:
    fast-safe-stringify: private
  /fastq/1.19.1:
    fastq: private
  /fb-watchman/2.0.2:
    fb-watchman: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /fill-range/7.1.1:
    fill-range: private
  /finalhandler/1.2.0:
    finalhandler: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flatted/3.3.3:
    flatted: private
  /for-each/0.3.5:
    for-each: private
  /foreground-child/3.3.1:
    foreground-child: private
  /form-data/4.0.2:
    form-data: private
  /formidable/3.5.4:
    formidable: private
  /forwarded/0.2.0:
    forwarded: private
  /fresh/0.5.2:
    fresh: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /functions-have-names/1.2.3:
    functions-have-names: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-east-asian-width/1.3.0:
    get-east-asian-width: private
  /get-func-name/2.0.2:
    get-func-name: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-package-type/0.1.0:
    get-package-type: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/6.0.1:
    get-stream: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob/10.4.5:
    glob: private
  /globals/13.24.0:
    globals: private
  /globby/11.1.0:
    globby: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /graphemer/1.4.0:
    graphemer: private
  /has-bigints/1.1.0:
    has-bigints: private
  /has-flag/3.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hasown/2.0.2:
    hasown: private
  /html-encoding-sniffer/4.0.0:
    html-encoding-sniffer: private
  /html-escaper/2.0.2:
    html-escaper: private
  /http-errors/2.0.0:
    http-errors: private
  /http-proxy-agent/7.0.2:
    http-proxy-agent: private
  /https-proxy-agent/7.0.6:
    https-proxy-agent: private
  /human-signals/2.1.0:
    human-signals: private
  /iconv-lite/0.4.24:
    iconv-lite: private
  /ignore-by-default/1.0.1:
    ignore-by-default: private
  /ignore/5.3.2:
    ignore: private
  /import-fresh/3.3.1:
    import-fresh: private
  /import-local/3.2.0:
    import-local: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indent-string/4.0.0:
    indent-string: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /internal-slot/1.1.0:
    internal-slot: private
  /ipaddr.js/1.9.1:
    ipaddr.js: private
  /is-arguments/1.2.0:
    is-arguments: private
  /is-array-buffer/3.0.5:
    is-array-buffer: private
  /is-arrayish/0.2.1:
    is-arrayish: private
  /is-bigint/1.1.0:
    is-bigint: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-boolean-object/1.2.2:
    is-boolean-object: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-date-object/1.1.0:
    is-date-object: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-fullwidth-code-point/4.0.0:
    is-fullwidth-code-point: private
  /is-generator-fn/2.1.0:
    is-generator-fn: private
  /is-glob/4.0.3:
    is-glob: private
  /is-map/2.0.3:
    is-map: private
  /is-number-object/1.1.1:
    is-number-object: private
  /is-number/7.0.0:
    is-number: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-potential-custom-element-name/1.0.1:
    is-potential-custom-element-name: private
  /is-regex/1.2.1:
    is-regex: private
  /is-set/2.0.3:
    is-set: private
  /is-shared-array-buffer/1.0.4:
    is-shared-array-buffer: private
  /is-stream/2.0.1:
    is-stream: private
  /is-string/1.1.1:
    is-string: private
  /is-symbol/1.1.1:
    is-symbol: private
  /is-weakmap/2.0.2:
    is-weakmap: private
  /is-weakset/2.0.4:
    is-weakset: private
  /isarray/2.0.5:
    isarray: private
  /isexe/2.0.0:
    isexe: private
  /istanbul-lib-coverage/3.2.2:
    istanbul-lib-coverage: private
  /istanbul-lib-instrument/6.0.3:
    istanbul-lib-instrument: private
  /istanbul-lib-report/3.0.1:
    istanbul-lib-report: private
  /istanbul-lib-source-maps/4.0.1:
    istanbul-lib-source-maps: private
  /istanbul-reports/3.1.7:
    istanbul-reports: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jest-changed-files/29.7.0:
    jest-changed-files: private
  /jest-circus/29.7.0:
    jest-circus: private
  /jest-cli/29.7.0(@types/node@20.12.7):
    jest-cli: private
  /jest-config/29.7.0(@types/node@20.12.7):
    jest-config: private
  /jest-diff/29.7.0:
    jest-diff: private
  /jest-docblock/29.7.0:
    jest-docblock: private
  /jest-each/29.7.0:
    jest-each: private
  /jest-environment-node/29.7.0:
    jest-environment-node: private
  /jest-get-type/29.6.3:
    jest-get-type: private
  /jest-haste-map/29.7.0:
    jest-haste-map: private
  /jest-leak-detector/29.7.0:
    jest-leak-detector: private
  /jest-matcher-utils/29.7.0:
    jest-matcher-utils: private
  /jest-message-util/29.7.0:
    jest-message-util: private
  /jest-mock/29.7.0:
    jest-mock: private
  /jest-pnp-resolver/1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  /jest-regex-util/29.6.3:
    jest-regex-util: private
  /jest-resolve-dependencies/29.7.0:
    jest-resolve-dependencies: private
  /jest-resolve/29.7.0:
    jest-resolve: private
  /jest-runner/29.7.0:
    jest-runner: private
  /jest-runtime/29.7.0:
    jest-runtime: private
  /jest-snapshot/29.7.0:
    jest-snapshot: private
  /jest-util/29.7.0:
    jest-util: private
  /jest-validate/29.7.0:
    jest-validate: private
  /jest-watcher/29.7.0:
    jest-watcher: private
  /jest-worker/29.7.0:
    jest-worker: private
  /joycon/3.1.1:
    joycon: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsdom/24.1.0:
    jsdom: private
  /jsesc/3.1.0:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-parse-even-better-errors/2.3.1:
    json-parse-even-better-errors: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json5/2.2.3:
    json5: private
  /keyv/4.5.4:
    keyv: private
  /kleur/3.0.3:
    kleur: private
  /leven/3.1.0:
    leven: private
  /levn/0.4.1:
    levn: private
  /lilconfig/3.0.0:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /listr2/8.0.1:
    listr2: private
  /load-tsconfig/0.2.5:
    load-tsconfig: private
  /local-pkg/0.5.1:
    local-pkg: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.memoize/4.1.2:
    lodash.memoize: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.sortby/4.7.0:
    lodash.sortby: private
  /lodash/4.17.21:
    lodash: private
  /log-update/6.1.0:
    log-update: private
  /loose-envify/1.4.0:
    loose-envify: private
  /loupe/2.3.7:
    loupe: private
  /lru-cache/10.4.3:
    lru-cache: private
  /lz-string/1.5.0:
    lz-string: private
  /magic-string/0.30.17:
    magic-string: private
  /make-dir/4.0.0:
    make-dir: private
  /make-error/1.3.6:
    make-error: private
  /makeerror/1.0.12:
    makeerror: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /media-typer/0.3.0:
    media-typer: private
  /merge-descriptors/1.0.1:
    merge-descriptors: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /methods/1.1.2:
    methods: private
  /micromatch/4.0.5:
    micromatch: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mime/1.6.0:
    mime: private
  /mimic-fn/2.1.0:
    mimic-fn: private
  /mimic-function/5.0.1:
    mimic-function: private
  /min-indent/1.0.1:
    min-indent: private
  /minimatch/3.1.2:
    minimatch: private
  /minipass/7.1.2:
    minipass: private
  /mlly/1.7.4:
    mlly: private
  /ms/2.0.0:
    ms: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /natural-compare/1.4.0:
    natural-compare: private
  /negotiator/0.6.3:
    negotiator: private
  /node-int64/0.4.0:
    node-int64: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-path/3.0.0:
    normalize-path: private
  /npm-run-path/4.0.1:
    npm-run-path: private
  /nwsapi/2.2.20:
    nwsapi: private
  /object-assign/4.1.1:
    object-assign: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-is/1.1.6:
    object-is: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.7:
    object.assign: private
  /on-finished/2.4.1:
    on-finished: private
  /once/1.4.0:
    once: private
  /onetime/5.1.2:
    onetime: private
  /optionator/0.9.4:
    optionator: private
  /p-limit/5.0.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /p-try/2.2.0:
    p-try: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-json/5.2.0:
    parse-json: private
  /parse5/7.3.0:
    parse5: private
  /parseurl/1.3.3:
    parseurl: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /path-to-regexp/0.1.7:
    path-to-regexp: private
  /path-type/4.0.0:
    path-type: private
  /pathe/1.1.2:
    pathe: private
  /pathval/1.1.1:
    pathval: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/2.3.1:
    picomatch: private
  /pidtree/0.6.0:
    pidtree: private
  /pirates/4.0.7:
    pirates: private
  /pkg-dir/4.2.0:
    pkg-dir: private
  /pkg-types/1.3.1:
    pkg-types: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-load-config/4.0.2:
    postcss-load-config: private
  /postcss/8.5.3:
    postcss: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prettier-linter-helpers/1.0.0:
    prettier-linter-helpers: public
  /pretty-format/29.7.0:
    pretty-format: private
  /prompts/2.4.2:
    prompts: private
  /proxy-addr/2.0.7:
    proxy-addr: private
  /psl/1.15.0:
    psl: private
  /pstree.remy/1.1.8:
    pstree.remy: private
  /punycode/2.3.1:
    punycode: private
  /pure-rand/6.1.0:
    pure-rand: private
  /qs/6.11.0:
    qs: private
  /querystringify/2.2.0:
    querystringify: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /range-parser/1.2.1:
    range-parser: private
  /raw-body/2.5.2:
    raw-body: private
  /react-dom/18.2.0(react@18.2.0):
    react-dom: private
  /react-is/18.3.1:
    react-is: private
  /react-refresh/0.14.2:
    react-refresh: private
  /react/18.2.0:
    react: private
  /readdirp/3.6.0:
    readdirp: private
  /redent/3.0.0:
    redent: private
  /regexp.prototype.flags/1.5.4:
    regexp.prototype.flags: private
  /require-directory/2.1.1:
    require-directory: private
  /requires-port/1.0.0:
    requires-port: private
  /resolve-cwd/3.0.0:
    resolve-cwd: private
  /resolve-from/5.0.0:
    resolve-from: private
  /resolve.exports/2.0.3:
    resolve.exports: private
  /resolve/1.22.10:
    resolve: private
  /restore-cursor/5.1.0:
    restore-cursor: private
  /reusify/1.1.0:
    reusify: private
  /rfdc/1.4.1:
    rfdc: private
  /rimraf/3.0.2:
    rimraf: private
  /rollup/4.41.1:
    rollup: private
  /rrweb-cssom/0.7.1:
    rrweb-cssom: private
  /run-parallel/1.2.0:
    run-parallel: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /saxes/6.0.0:
    saxes: private
  /scheduler/0.23.2:
    scheduler: private
  /semver/7.7.2:
    semver: private
  /send/0.18.0:
    send: private
  /serve-static/1.15.0:
    serve-static: private
  /set-function-length/1.2.2:
    set-function-length: private
  /set-function-name/2.0.2:
    set-function-name: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /siginfo/2.0.0:
    siginfo: private
  /signal-exit/3.0.7:
    signal-exit: private
  /simple-update-notifier/2.0.0:
    simple-update-notifier: private
  /sisteransi/1.0.5:
    sisteransi: private
  /slash/3.0.0:
    slash: private
  /slice-ansi/5.0.0:
    slice-ansi: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-support/0.5.13:
    source-map-support: private
  /source-map/0.8.0-beta.0:
    source-map: private
  /sprintf-js/1.0.3:
    sprintf-js: private
  /stack-utils/2.0.6:
    stack-utils: private
  /stackback/0.0.2:
    stackback: private
  /statuses/2.0.1:
    statuses: private
  /std-env/3.9.0:
    std-env: private
  /stop-iteration-iterator/1.1.0:
    stop-iteration-iterator: private
  /string-argv/0.3.2:
    string-argv: private
  /string-length/4.0.2:
    string-length: private
  /string-width/4.2.3:
    string-width-cjs: private
  /string-width/7.2.0:
    string-width: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom/4.0.0:
    strip-bom: private
  /strip-final-newline/2.0.0:
    strip-final-newline: private
  /strip-indent/3.0.0:
    strip-indent: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /strip-literal/2.1.1:
    strip-literal: private
  /sucrase/3.35.0:
    sucrase: private
  /superagent/10.2.1:
    superagent: private
  /supports-color/5.5.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /symbol-tree/3.2.4:
    symbol-tree: private
  /synckit/0.8.8:
    synckit: private
  /test-exclude/6.0.0:
    test-exclude: private
  /text-table/0.2.0:
    text-table: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /tinybench/2.9.0:
    tinybench: private
  /tinypool/0.8.4:
    tinypool: private
  /tinyspy/2.2.1:
    tinyspy: private
  /tmpl/1.0.5:
    tmpl: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /toidentifier/1.0.1:
    toidentifier: private
  /touch/3.1.1:
    touch: private
  /tough-cookie/4.1.4:
    tough-cookie: private
  /tr46/5.1.1:
    tr46: private
  /tree-kill/1.2.2:
    tree-kill: private
  /ts-api-utils/1.4.3(typescript@5.4.3):
    ts-api-utils: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /ts-jest/29.1.2(@babel/core@7.27.1)(esbuild@0.19.12)(jest@29.7.0)(typescript@5.4.3):
    ts-jest: private
  /tslib/2.8.1:
    tslib: private
  /turbo-darwin-64/1.13.2:
    turbo-darwin-64: private
  /turbo-darwin-arm64/1.13.2:
    turbo-darwin-arm64: private
  /turbo-linux-64/1.13.2:
    turbo-linux-64: private
  /turbo-linux-arm64/1.13.2:
    turbo-linux-arm64: private
  /turbo-windows-64/1.13.2:
    turbo-windows-64: private
  /turbo-windows-arm64/1.13.2:
    turbo-windows-arm64: private
  /type-check/0.4.0:
    type-check: private
  /type-detect/4.1.0:
    type-detect: private
  /type-fest/0.20.2:
    type-fest: private
  /type-is/1.6.18:
    type-is: private
  /ufo/1.6.1:
    ufo: private
  /undefsafe/2.0.5:
    undefsafe: private
  /undici-types/5.26.5:
    undici-types: private
  /universalify/0.2.0:
    universalify: private
  /unpipe/1.0.0:
    unpipe: private
  /update-browserslist-db/1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  /uri-js/4.4.1:
    uri-js: private
  /url-parse/1.5.10:
    url-parse: private
  /utils-merge/1.0.1:
    utils-merge: private
  /v8-to-istanbul/9.3.0:
    v8-to-istanbul: private
  /vary/1.1.2:
    vary: private
  /vite-node/1.5.0(@types/node@20.12.7):
    vite-node: private
  /vite/5.2.2(@types/node@20.12.7):
    vite: private
  /w3c-xmlserializer/5.0.0:
    w3c-xmlserializer: private
  /walker/1.0.8:
    walker: private
  /webidl-conversions/7.0.0:
    webidl-conversions: private
  /whatwg-encoding/3.1.1:
    whatwg-encoding: private
  /whatwg-mimetype/4.0.0:
    whatwg-mimetype: private
  /whatwg-url/14.2.0:
    whatwg-url: private
  /which-boxed-primitive/1.1.1:
    which-boxed-primitive: private
  /which-collection/1.0.2:
    which-collection: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/2.0.2:
    which: private
  /why-is-node-running/2.3.0:
    why-is-node-running: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrap-ansi/9.0.0:
    wrap-ansi: private
  /wrappy/1.0.2:
    wrappy: private
  /write-file-atomic/4.0.2:
    write-file-atomic: private
  /ws/8.18.2:
    ws: private
  /xml-name-validator/5.0.0:
    xml-name-validator: private
  /xmlchars/2.2.0:
    xmlchars: private
  /y18n/5.0.8:
    y18n: private
  /yallist/3.1.1:
    yallist: private
  /yaml/2.3.4:
    yaml: private
  /yargs-parser/21.1.1:
    yargs-parser: private
  /yargs/17.7.2:
    yargs: private
  /yocto-queue/1.2.1:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.4
pendingBuilds: []
prunedAt: Sun, 25 May 2025 12:47:32 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@esbuild/aix-ppc64/0.19.12
  - /@esbuild/aix-ppc64/0.20.2
  - /@esbuild/android-arm/0.19.12
  - /@esbuild/android-arm/0.20.2
  - /@esbuild/android-arm64/0.19.12
  - /@esbuild/android-arm64/0.20.2
  - /@esbuild/android-x64/0.19.12
  - /@esbuild/android-x64/0.20.2
  - /@esbuild/darwin-x64/0.19.12
  - /@esbuild/darwin-x64/0.20.2
  - /@esbuild/freebsd-arm64/0.19.12
  - /@esbuild/freebsd-arm64/0.20.2
  - /@esbuild/freebsd-x64/0.19.12
  - /@esbuild/freebsd-x64/0.20.2
  - /@esbuild/linux-arm/0.19.12
  - /@esbuild/linux-arm/0.20.2
  - /@esbuild/linux-arm64/0.19.12
  - /@esbuild/linux-arm64/0.20.2
  - /@esbuild/linux-ia32/0.19.12
  - /@esbuild/linux-ia32/0.20.2
  - /@esbuild/linux-loong64/0.19.12
  - /@esbuild/linux-loong64/0.20.2
  - /@esbuild/linux-mips64el/0.19.12
  - /@esbuild/linux-mips64el/0.20.2
  - /@esbuild/linux-ppc64/0.19.12
  - /@esbuild/linux-ppc64/0.20.2
  - /@esbuild/linux-riscv64/0.19.12
  - /@esbuild/linux-riscv64/0.20.2
  - /@esbuild/linux-s390x/0.19.12
  - /@esbuild/linux-s390x/0.20.2
  - /@esbuild/linux-x64/0.19.12
  - /@esbuild/linux-x64/0.20.2
  - /@esbuild/netbsd-x64/0.19.12
  - /@esbuild/netbsd-x64/0.20.2
  - /@esbuild/openbsd-x64/0.19.12
  - /@esbuild/openbsd-x64/0.20.2
  - /@esbuild/sunos-x64/0.19.12
  - /@esbuild/sunos-x64/0.20.2
  - /@esbuild/win32-arm64/0.19.12
  - /@esbuild/win32-arm64/0.20.2
  - /@esbuild/win32-ia32/0.19.12
  - /@esbuild/win32-ia32/0.20.2
  - /@esbuild/win32-x64/0.19.12
  - /@esbuild/win32-x64/0.20.2
  - /@rollup/rollup-android-arm-eabi/4.41.1
  - /@rollup/rollup-android-arm64/4.41.1
  - /@rollup/rollup-darwin-x64/4.41.1
  - /@rollup/rollup-freebsd-arm64/4.41.1
  - /@rollup/rollup-freebsd-x64/4.41.1
  - /@rollup/rollup-linux-arm-gnueabihf/4.41.1
  - /@rollup/rollup-linux-arm-musleabihf/4.41.1
  - /@rollup/rollup-linux-arm64-gnu/4.41.1
  - /@rollup/rollup-linux-arm64-musl/4.41.1
  - /@rollup/rollup-linux-loongarch64-gnu/4.41.1
  - /@rollup/rollup-linux-powerpc64le-gnu/4.41.1
  - /@rollup/rollup-linux-riscv64-gnu/4.41.1
  - /@rollup/rollup-linux-riscv64-musl/4.41.1
  - /@rollup/rollup-linux-s390x-gnu/4.41.1
  - /@rollup/rollup-linux-x64-gnu/4.41.1
  - /@rollup/rollup-linux-x64-musl/4.41.1
  - /@rollup/rollup-win32-arm64-msvc/4.41.1
  - /@rollup/rollup-win32-ia32-msvc/4.41.1
  - /@rollup/rollup-win32-x64-msvc/4.41.1
  - /turbo-darwin-64/1.13.2
  - /turbo-linux-64/1.13.2
  - /turbo-linux-arm64/1.13.2
  - /turbo-windows-64/1.13.2
  - /turbo-windows-arm64/1.13.2
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
