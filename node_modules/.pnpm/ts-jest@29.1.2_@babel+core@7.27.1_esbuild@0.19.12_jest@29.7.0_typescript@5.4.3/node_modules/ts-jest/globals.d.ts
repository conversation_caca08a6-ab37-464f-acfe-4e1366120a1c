declare global {
  const beforeAll: typeof import('@jest/globals').beforeAll
  const beforeEach: typeof import('@jest/globals').beforeEach
  const afterAll: typeof import('@jest/globals').afterAll
  const afterEach: typeof import('@jest/globals').afterEach
  const describe: typeof import('@jest/globals').describe
  const fdescribe: typeof import('@jest/globals').fdescribe
  const xdescribe: typeof import('@jest/globals').xdescribe
  const it: typeof import('@jest/globals').it
  const fit: typeof import('@jest/globals').fit
  const xit: typeof import('@jest/globals').xit
  const test: typeof import('@jest/globals').test
  const xtest: typeof import('@jest/globals').xtest
  const expect: typeof import('@jest/globals').expect
  const jest: typeof import('@jest/globals').jest
}

export {}
