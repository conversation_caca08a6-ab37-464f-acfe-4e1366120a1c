import { d as createCLI } from './vendor/cac.qnW6GNL2.js';
import 'pathe';
import 'events';
import 'picocolors';
import './vendor/index.ir9i0ywP.js';
import 'std-env';
import '@vitest/runner/utils';
import '@vitest/utils';
import './vendor/global.CkGT_TMy.js';
import './vendor/index.kSaPvGW6.js';
import 'node:fs';
import './vendor/tasks.IknbGB2n.js';
import 'node:perf_hooks';
import './chunks/runtime-console.kbFEN7E-.js';
import 'node:stream';
import 'node:console';
import 'node:path';
import './vendor/date.Ns1pGd_X.js';
import './vendor/base.Xt0Omgh7.js';
import '@vitest/utils/source-map';
import 'node:os';
import 'node:module';
import 'node:fs/promises';
import 'execa';
import 'node:url';
import 'path';
import 'fs';
import 'module';
import 'vite';
import 'acorn-walk';
import 'node:process';
import './vendor/_commonjsHelpers.jjO7Zipk.js';
import 'assert';
import 'node:crypto';
import 'vite-node/utils';
import './vendor/constants.5J7I254_.js';
import './vendor/coverage.E7sG1b3r.js';
import './vendor/index.GVFv9dZ0.js';
import 'node:worker_threads';
import 'os';
import './vendor/index.xL8XjTLv.js';
import 'util';
import 'stream';
import 'vite-node/client';
import '@vitest/snapshot/manager';
import 'vite-node/server';
import './path.js';
import './vendor/index.8bPxjt7g.js';
import 'zlib';
import 'buffer';
import 'crypto';
import 'https';
import 'http';
import 'net';
import 'tls';
import 'url';
import 'node:v8';
import 'node:events';
import 'tinypool';
import 'local-pkg';
import 'magic-string';
import '@vitest/utils/ast';
import 'strip-literal';
import 'node:readline';
import 'readline';

createCLI().parse();
