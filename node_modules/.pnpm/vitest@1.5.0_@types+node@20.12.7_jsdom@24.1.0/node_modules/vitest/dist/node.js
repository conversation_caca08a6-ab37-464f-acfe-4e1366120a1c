export { b as VitestPackageInstaller, V as VitestPlugin, a as createMethodsRPC, c as createVitest, p as parseCLI, r as registerConsoleShortcuts, s as startVitest } from './vendor/cac.qnW6GNL2.js';
export { B as BaseSequencer } from './vendor/index.kSaPvGW6.js';
import 'pathe';
import 'events';
import 'picocolors';
import './vendor/index.ir9i0ywP.js';
import 'std-env';
import '@vitest/runner/utils';
import '@vitest/utils';
import './vendor/global.CkGT_TMy.js';
import './vendor/constants.5J7I254_.js';
import './vendor/coverage.E7sG1b3r.js';
import './vendor/index.GVFv9dZ0.js';
import 'node:console';
import 'vite';
import 'node:path';
import 'node:url';
import 'node:process';
import 'node:fs';
import 'node:worker_threads';
import './vendor/_commonjsHelpers.jjO7Zipk.js';
import 'os';
import 'path';
import './vendor/index.xL8XjTLv.js';
import 'util';
import 'stream';
import 'fs';
import 'vite-node/client';
import '@vitest/snapshot/manager';
import 'vite-node/server';
import './path.js';
import './vendor/index.8bPxjt7g.js';
import 'zlib';
import 'buffer';
import 'crypto';
import 'https';
import 'http';
import 'net';
import 'tls';
import 'url';
import '@vitest/utils/source-map';
import './vendor/base.Xt0Omgh7.js';
import 'node:v8';
import 'node:os';
import 'node:events';
import 'tinypool';
import 'local-pkg';
import 'node:crypto';
import 'vite-node/utils';
import 'magic-string';
import 'acorn-walk';
import '@vitest/utils/ast';
import 'strip-literal';
import 'node:module';
import 'node:readline';
import 'readline';
import './vendor/tasks.IknbGB2n.js';
import 'node:perf_hooks';
import './chunks/runtime-console.kbFEN7E-.js';
import 'node:stream';
import './vendor/date.Ns1pGd_X.js';
import 'node:fs/promises';
import 'execa';
import 'module';
import 'assert';
