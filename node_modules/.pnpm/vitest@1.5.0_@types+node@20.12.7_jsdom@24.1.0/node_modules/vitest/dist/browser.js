export { processError, startTests } from '@vitest/runner';
export { l as loadDiffConfig, a as loadSnapshotSerializers, s as setupCommonEnv } from './vendor/setup-common.A1De6efh.js';
export { g as getCoverageProvider, a as startCoverageInsideWorker, s as stopCoverageInsideWorker, t as takeCoverageInsideWorker } from './vendor/coverage.E7sG1b3r.js';
import '@vitest/utils';
import '@vitest/snapshot';
import './vendor/run-once.Olz_Zkd8.js';
import './vendor/global.CkGT_TMy.js';
