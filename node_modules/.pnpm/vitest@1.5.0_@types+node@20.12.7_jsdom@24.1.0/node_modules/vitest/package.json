{"name": "vitest", "type": "module", "version": "1.5.0", "description": "Next generation testing framework powered by Vite", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/vitest"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "keywords": ["vite", "vite-node", "vitest", "test", "jest"], "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./index.d.cts", "default": "./index.cjs"}}, "./*": "./*", "./globals": {"types": "./globals.d.ts"}, "./jsdom": {"types": "./jsdom.d.ts"}, "./importMeta": {"types": "./importMeta.d.ts"}, "./import-meta": {"types": "./import-meta.d.ts"}, "./node": {"types": "./dist/node.d.ts", "default": "./dist/node.js"}, "./execute": {"types": "./dist/execute.d.ts", "default": "./dist/execute.js"}, "./workers": {"types": "./dist/workers.d.ts", "import": "./dist/workers.js"}, "./browser": {"types": "./dist/browser.d.ts", "default": "./dist/browser.js"}, "./runners": {"types": "./dist/runners.d.ts", "default": "./dist/runners.js"}, "./suite": {"types": "./dist/suite.d.ts", "default": "./dist/suite.js"}, "./environments": {"types": "./dist/environments.d.ts", "default": "./dist/environments.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}, "./config": {"types": "./config.d.ts", "require": "./dist/config.cjs", "default": "./dist/config.js"}, "./coverage": {"types": "./coverage.d.ts", "default": "./dist/coverage.js"}, "./reporters": {"types": "./dist/reporters.d.ts", "default": "./dist/reporters.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"vitest": "./vitest.mjs"}, "files": ["*.cjs", "*.d.cts", "*.d.ts", "*.mjs", "bin", "dist"], "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"@edge-runtime/vm": "*", "@types/node": "^18.0.0 || >=20.0.0", "happy-dom": "*", "jsdom": "*", "@vitest/ui": "1.5.0", "@vitest/browser": "1.5.0"}, "peerDependenciesMeta": {"@edge-runtime/vm": {"optional": true}, "@types/node": {"optional": true}, "@vitest/browser": {"optional": true}, "@vitest/ui": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}}, "dependencies": {"acorn-walk": "^8.3.2", "chai": "^4.3.10", "debug": "^4.3.4", "execa": "^8.0.1", "local-pkg": "^0.5.0", "magic-string": "^0.30.5", "pathe": "^1.1.1", "picocolors": "^1.0.0", "std-env": "^3.5.0", "strip-literal": "^2.0.0", "tinybench": "^2.5.1", "tinypool": "^0.8.3", "vite": "^5.0.0", "why-is-node-running": "^2.2.2", "@vitest/expect": "1.5.0", "@vitest/snapshot": "1.5.0", "@vitest/spy": "1.5.0", "@vitest/utils": "1.5.0", "vite-node": "1.5.0", "@vitest/runner": "1.5.0"}, "devDependencies": {"@ampproject/remapping": "^2.2.1", "@antfu/install-pkg": "^0.3.1", "@edge-runtime/vm": "^3.1.8", "@sinonjs/fake-timers": "11.1.0", "@types/estree": "^1.0.5", "@types/istanbul-lib-coverage": "^2.0.6", "@types/istanbul-reports": "^3.0.4", "@types/jsdom": "^21.1.6", "@types/micromatch": "^4.0.6", "@types/node": "^20.11.5", "@types/prompts": "^2.4.9", "@types/sinonjs__fake-timers": "^8.1.5", "birpc": "0.2.15", "cac": "^6.7.14", "chai-subset": "^1.6.0", "cli-truncate": "^4.0.0", "expect-type": "^0.17.3", "fast-glob": "^3.3.2", "find-up": "^6.3.0", "flatted": "^3.2.9", "get-tsconfig": "^4.7.3", "happy-dom": "^14.3.10", "jsdom": "^24.0.0", "log-update": "^5.0.1", "micromatch": "^4.0.5", "p-limit": "^5.0.0", "pretty-format": "^29.7.0", "prompts": "^2.4.2", "strip-ansi": "^7.1.0", "ws": "^8.14.2"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "NODE_OPTIONS=\"--max-old-space-size=8192\" rollup -c --watch -m inline"}}