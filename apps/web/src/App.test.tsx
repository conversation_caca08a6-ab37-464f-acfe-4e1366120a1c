import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from './App';

// Mock React Testing Library for Vitest
import '@testing-library/jest-dom';

describe('App Component', () => {
  it('renders welcome message', () => {
    render(<App />);
    expect(screen.getByText('Workflow Mapper')).toBeInTheDocument();
    expect(
      screen.getByText('Welcome to the Workflow Mapper application.')
    ).toBeInTheDocument();
  });
});
