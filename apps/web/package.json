{"name": "@workflow-mapper/web", "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest run", "type-check": "tsc --noEmit"}, "dependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@types/react": "18.2.79", "@types/react-dom": "18.2.25", "@vitejs/plugin-react": "4.2.1", "typescript": "5.4.3", "vite": "5.2.2", "vitest": "1.5.0", "@testing-library/react": "14.3.1", "@testing-library/jest-dom": "6.4.5", "jsdom": "24.1.0"}}