
> @workflow-mapper/web@0.0.1 test /Users/<USER>/tmp/kloudi-swe-agent/apps/web
> vitest run


[7m[1m[36m RUN [39m[22m[27m [36mv1.5.0[39m [90m/Users/<USER>/tmp/kloudi-swe-agent/apps/web[39m

[?25l [32m✓[39m [2msrc/[22mApp[2m.test.tsx[22m[2m (1)[22m
   [32m✓[39m App Component[2m (1)[22m
     [32m✓[39m renders welcome message
[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mApp[2m.test.tsx[22m[2m (1)[22m
   [32m✓[39m App Component[2m (1)[22m
     [32m✓[39m renders welcome message

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m   Start at [22m 18:32:04
[2m   Duration [22m 849ms[2m (transform 35ms, setup 66ms, collect 116ms, tests 13ms, environment 298ms, prepare 197ms)[22m

[?25h[?25h
