import request from 'supertest';
import express from 'express';

// Create a test app with the same setup as our main app
const app = express();
app.get('/health', (_, res) => {
  res.json({ status: 'ok' });
});

describe('API Health Endpoint', () => {
  it('should return 200 OK with status', async () => {
    const response = await request(app).get('/health').expect(200);

    expect(response.body).toEqual({ status: 'ok' });
  });
});
