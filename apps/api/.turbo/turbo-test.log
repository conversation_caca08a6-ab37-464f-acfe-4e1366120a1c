
> @workflow-mapper/api@0.0.1 test /Users/<USER>/tmp/kloudi-swe-agent/apps/api
> jest

[1m[2mDetermining test suites to run...[22m[22m[999D[K

[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1Ats-jest[ts-jest-transformer] (WARN) Define `ts-jest` config under `globals` is deprecated. Please do
transform: {
    <transform_regex>: ['ts-jest', { /* ts-jest config goes here in Jest */ }],
},

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A[0m[7m[1m[32m PASS [39m[22m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A  API Health Endpoint

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A    [32m✓[39m [2mshould return 200 OK with status (62 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2msrc/[22m[1mindex.test.ts[22m

[K
[1A
[K
[1A[999D[K[1mTest Suites: [22m[1m[32m1 passed[39m[22m, 1 total
[1mTests:       [22m[1m[32m1 passed[39m[22m, 1 total
[1mSnapshots:   [22m0 total
[1mTime:[22m        0.858 s, estimated 1 s
[2mRan all test suites[22m[2m.[22m
