#!/usr/bin/env bash
set -euo pipefail

echo "🔧 Running M0 Acceptance Tests..."

# Test 1: Clean install and build
echo "1️⃣ Testing clean install and build..."
pnpm install
pnpm build
echo "✅ Build successful"

# Test 2: Lint and type check
echo "2️⃣ Testing lint and type check..."
pnpm lint
pnpm type-check
echo "✅ Lint and type check passed"

# Test 3: Run tests
echo "3️⃣ Testing test suite..."
pnpm exec turbo run test --parallel
echo "✅ All tests passed"

# Test 4: API health check
echo "4️⃣ Testing API health endpoint..."
cd apps/api
node dist/index.js &
API_PID=$!
sleep 2

# Test the health endpoint
if curl -fs http://localhost:3000/health | grep '"status":"ok"' >/dev/null; then
    echo "✅ API health endpoint working"
else
    echo "❌ API health endpoint failed"
    kill $API_PID 2>/dev/null || true
    exit 1
fi

# Clean up
kill $API_PID 2>/dev/null || true
cd ../..

echo "🎉 All M0 acceptance tests passed!"
echo "✅ Repository skeleton is ready for development"
