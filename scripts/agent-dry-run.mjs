#!/usr/bin/env node

/**
 * Agent Dry-Run Script for M0
 * 
 * This script simulates an agent completing the M0 scaffold process
 * by validating that all required files and configurations are in place.
 */

import { existsSync } from 'fs';
import { readFileSync } from 'fs';
import { join } from 'path';

const requiredFiles = [
  // Root configuration
  'package.json',
  'pnpm-workspace.yaml',
  'tsconfig.json',
  '.eslintrc.cjs',
  '.prettierrc',
  'turbo.json',
  'Dockerfile',
  'docker-compose.yml',
  
  // Documentation
  'README.md',
  'CONTRIBUTING.md',
  'CHANGELOG.md',
  'SECURITY.md',
  
  // Scripts
  'scripts/m0-acceptance.sh',
  
  // CI/CD
  '.github/workflows/ci.yml',
  
  // Husky
  '.husky/pre-commit',
  
  // API package
  'apps/api/package.json',
  'apps/api/src/index.ts',
  'apps/api/src/app.ts',
  'apps/api/src/index.test.ts',
  'apps/api/tsconfig.json',
  'apps/api/jest.config.js',
  
  // Web package
  'apps/web/package.json',
  'apps/web/src/main.tsx',
  'apps/web/src/App.tsx',
  'apps/web/src/App.test.tsx',
  'apps/web/src/test-setup.ts',
  'apps/web/index.html',
  'apps/web/tsconfig.json',
  'apps/web/tsconfig.node.json',
  'apps/web/vite.config.ts',
  
  // Shared package
  'packages/shared/package.json',
  'packages/shared/src/index.ts',
  'packages/shared/src/Result.ts',
  'packages/shared/src/Result.test.ts',
  'packages/shared/tsconfig.json',
  'packages/shared/vitest.config.ts',
  
  // Milestone specification
  'docs/tech-specs/milestones/milestone-M0.mdx',
];

const requiredScripts = [
  'dev:api',
  'dev:web', 
  'build',
  'lint',
  'test',
  'test:coverage',
  'type-check',
  'agent:dry-run',
];

console.log('🤖 Running Agent Dry-Run for M0...\n');

let errors = 0;

// Check required files exist
console.log('📁 Checking required files...');
for (const file of requiredFiles) {
  if (!existsSync(file)) {
    console.log(`❌ Missing file: ${file}`);
    errors++;
  } else {
    console.log(`✅ Found: ${file}`);
  }
}

// Check package.json scripts
console.log('\n📜 Checking package.json scripts...');
try {
  const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));
  for (const script of requiredScripts) {
    if (!packageJson.scripts[script]) {
      console.log(`❌ Missing script: ${script}`);
      errors++;
    } else {
      console.log(`✅ Script found: ${script}`);
    }
  }
} catch (error) {
  console.log(`❌ Error reading package.json: ${error.message}`);
  errors++;
}

// Check workspace configuration
console.log('\n🏗️ Checking workspace configuration...');
try {
  const workspace = readFileSync('pnpm-workspace.yaml', 'utf8');
  if (workspace.includes('apps/*') && workspace.includes('packages/*')) {
    console.log('✅ Workspace configuration valid');
  } else {
    console.log('❌ Invalid workspace configuration');
    errors++;
  }
} catch (error) {
  console.log(`❌ Error reading workspace config: ${error.message}`);
  errors++;
}

// Summary
console.log('\n📊 Dry-Run Summary:');
if (errors === 0) {
  console.log('🎉 All checks passed! M0 scaffold is complete.');
  console.log('✅ Agent dry-run successful');
  process.exit(0);
} else {
  console.log(`❌ ${errors} error(s) found`);
  console.log('🔧 Please fix the issues above before proceeding');
  process.exit(1);
}
