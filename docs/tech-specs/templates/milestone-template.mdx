---
title: Milestone <ID> — <One-line scope>
description: <Short paragraph of intent>
created: <YYYY-MM-DD>
version: 0.0.0
status: Draft
tags: [milestone]
authors: []
---

import { Callout } from '@/components/Callout'

<Callout emoji="🚧">
<strong>Draft.</strong> Replace placeholders before PR.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
# pin exact versions or "inherit-from-root"
```

---

## 🎯 Definition of Done

<text>

---

## 📦 Deliverables

| Path | Must contain … |
|------|----------------|
|      |                |

---

## 🗂 Directory / API Diagram

```text
# Tree or sequence diagram as needed
```

---

## 🧠 Key Decisions

| Topic  | Decision | Rationale |
|--------|----------|-----------|
|        |          |           |

---

## ✅ Success Criteria

**AUTHORITATIVE TEST**: `bash scripts/<milestone>-acceptance.sh`

The milestone is complete when the acceptance script passes (exit code 0). This script is the single source of truth for all validation requirements.

**The script validates:**
- [ ] <Validation criterion 1>
- [ ] <Validation criterion 2>
- [ ] <Validation criterion 3>

---

## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation. These rules prevent common implementation issues and ensure quality standards.

### Agent Configuration Templates

#### For Claude/Anthropic (System Instructions)
```markdown
You are implementing Milestone <ID>: <Title>. Follow these mandatory process requirements:

PRE-IMPLEMENTATION RULES:
- MUST create work-log/milestone-<id>/requirement-checklist.md before starting
- MUST validate ALL success criteria are understood and testable
- MUST plan complete dependency compatibility matrix upfront
- MUST design shared configuration strategy to prevent duplication

DURING IMPLEMENTATION RULES:
- MUST update work logs in real-time, not after completion
- MUST update milestone specification during implementation with actual versions
- MUST use systematic requirement tracking with checklists
- MUST validate each success criterion immediately after implementation

POST-IMPLEMENTATION RULES:
- MUST run bash scripts/<milestone>-acceptance.sh immediately after implementation
- MUST update all documentation to reflect actual implementation
- MUST create comprehensive work log with lessons learned
- MUST commit with conventional commit messages including detailed descriptions

QUALITY ASSURANCE RULES:
- MUST implement ALL specified success criteria (no skipping)
- MUST use package managers instead of manual file editing
- MUST include comprehensive testing with appropriate coverage
- MUST validate all acceptance criteria before marking complete
```

#### For GitHub Copilot (Workspace Settings)
```json
{
  "github.copilot.chat.codeGeneration.instructions": [
    "Always create work-log/milestone-<id>/requirement-checklist.md before starting implementation",
    "Update work logs in real-time during implementation, not after completion",
    "Validate ALL success criteria immediately after implementation",
    "Update milestone specifications to reflect actual toolchain versions",
    "Use package managers for dependency management, never edit package files manually",
    "Include comprehensive testing with appropriate coverage thresholds",
    "Run complete acceptance validation immediately after implementation"
  ]
}
```

#### For Cursor (.cursor/rules)
```markdown
# Milestone <ID> Process Requirements

## Pre-Implementation
- Create work-log/milestone-<id>/requirement-checklist.md before starting
- Validate ALL success criteria are understood and testable
- Plan complete dependency compatibility matrix upfront

## During Implementation
- Update work logs in real-time, not after completion
- Update milestone specification during implementation
- Use systematic requirement tracking with checklists

## Post-Implementation
- Run bash scripts/<milestone>-acceptance.sh immediately after implementation
- Update all documentation to reflect actual implementation
- Create comprehensive work log with lessons learned

## Quality Assurance
- Implement ALL specified success criteria (no skipping)
- Use package managers instead of manual file editing
- Include comprehensive testing with appropriate coverage
- Validate all acceptance criteria before marking complete
```

#### For Custom Agents (YAML Configuration)
```yaml
agent_rules:
  milestone: "<ID>"
  title: "<Title>"

  pre_implementation:
    - "Create work-log/milestone-<id>/requirement-checklist.md before starting"
    - "Validate ALL success criteria are understood and testable"
    - "Plan complete dependency compatibility matrix upfront"
    - "Design shared configuration strategy to prevent duplication"

  during_implementation:
    - "Update work logs in real-time, not after completion"
    - "Update milestone specification during implementation with actual versions"
    - "Use systematic requirement tracking with checklists"
    - "Validate each success criterion immediately after implementation"

  post_implementation:
    - "Run bash scripts/<milestone>-acceptance.sh immediately after implementation"
    - "Update all documentation to reflect actual implementation"
    - "Create comprehensive work log with lessons learned"
    - "Commit with conventional commit messages including detailed descriptions"

  quality_assurance:
    - "Implement ALL specified success criteria (no skipping)"
    - "Use package managers instead of manual file editing"
    - "Include comprehensive testing with appropriate coverage"
    - "Validate all acceptance criteria before marking complete"
```

### Process Validation Checklist

Before starting implementation, verify:
- [ ] Agent rules configured in your AI assistant
- [ ] Pre-implementation checklist template ready
- [ ] Work log directory structure planned
- [ ] Understanding of all success criteria validated

During implementation, maintain:
- [ ] Real-time work log updates
- [ ] Systematic requirement tracking
- [ ] Immediate validation of completed criteria
- [ ] Documentation sync with actual implementation

After implementation, complete:
- [ ] Full acceptance test validation
- [ ] Comprehensive work log creation
- [ ] Documentation updates reflecting reality
- [ ] Process improvement analysis

---

## 🔨 Task Breakdown

| #  | Branch name | Checklist item |
|----|-------------|---------------|
|    |             |               |

---

## 🤖 CI Pipeline (ready-to-copy)

```yaml
# Workflow snippet
```

---

## 🧪 Acceptance Testing

**Single command validates everything:**

```bash
bash scripts/<milestone>-acceptance.sh
```

This script is the authoritative definition of all acceptance criteria. See the script implementation in the file stubs below.

---

## 🔄 Document History

| Version | Date | Changes | Author | Milestone Status |
|---------|------|---------|--------|------------------|
| 0.1.0 | <YYYY-MM-DD> | Initial specification | <author> | Draft |

### Status Progression
- **Draft** → **Approved** → **In Progress** → **In Review** → **Completed**

### Update Guidelines
- Increment version for significant changes (new requirements, scope changes)
- Update milestone status when implementation phase changes
- Document all major decisions and scope modifications
- Link to related ADRs when architectural decisions are made

---

## 📚 Related Documentation

### Cross-References
- **ADRs**: List any architectural decisions that impact this milestone
- **Dependencies**: Reference other milestones this depends on
- **Domains**: Link to relevant domain specifications

### External Resources
- **Technical References**: Links to external documentation, APIs, libraries
- **Research**: Background research and analysis documents

<Callout emoji="📝">
Must pass <Link href="../spec-checklist.mdx">spec-checklist</Link> & dry-run before moving to <code>status: Approved</code>.
</Callout>

<Callout emoji="🔗">
Remember to update the <Link href="../milestone-log.mdx">milestone-log.mdx</Link> when this milestone's status or progress changes.
</Callout>