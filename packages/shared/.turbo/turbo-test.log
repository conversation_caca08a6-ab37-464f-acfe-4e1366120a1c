
> @workflow-mapper/shared@0.0.1 test /Users/<USER>/tmp/kloudi-swe-agent/packages/shared
> vitest run


[7m[1m[36m RUN [39m[22m[27m [36mv1.5.0[39m [90m/Users/<USER>/tmp/kloudi-swe-agent/packages/shared[39m

 [32m✓[39m [2msrc/[22mResult[2m.test.ts[22m[2m (3)[22m
   [32m✓[39m Result Type[2m (3)[22m
     [32m✓[39m should create successful result
     [32m✓[39m should create error result
     [32m✓[39m should work with type guards

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m3 passed[39m[22m[90m (3)[39m
[2m   Start at [22m 18:32:04
[2m   Duration [22m 237ms[2m (transform 31ms, setup 0ms, collect 17ms, tests 1ms, environment 0ms, prepare 63ms)[22m

[?25h
