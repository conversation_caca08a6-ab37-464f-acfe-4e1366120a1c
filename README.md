# Workflow Mapper

[![CI](https://github.com/nitishMehrotra/workflow-mapper/actions/workflows/ci.yml/badge.svg)](https://github.com/nitishMehrotra/workflow-mapper/actions/workflows/ci.yml)

> **🎉 Milestone M0 Complete!** Repository skeleton and CI infrastructure ready for development.

A TypeScript monorepo for mapping and analyzing workflow structures using graph-based representations.

## 🚀 Quick Start

### Prerequisites

- Node.js 20.11.0+
- pnpm 8.15.4+
- Docker & Docker Compose

### Installation

```bash
# Clone the repository
git clone <repo-url>
cd workflow-mapper

# Install dependencies
pnpm install

# Start development servers
pnpm dev:api    # API server on :3000
pnpm dev:web    # Web app on :5173
```

### Development

```bash
# Run all tests
pnpm test

# Lint and format
pnpm lint

# Type checking
pnpm type-check

# Build all packages
pnpm build
```

### Docker

```bash
# Start full stack (API + Neo4j)
docker compose up

# Health checks
curl http://localhost:3000/health
curl http://localhost:5173/health  # Proxies to API
```

## 📁 Project Structure

```
workflow-mapper/
├── apps/
│   ├── api/          # Express API server
│   └── web/          # React web application
├── packages/
│   └── shared/       # Shared utilities and types
├── docs/
│   └── tech-specs/   # Technical specifications
└── scripts/          # Build and deployment scripts
```

## 🛠 Scripts

- `pnpm dev:api` - Start API development server
- `pnpm dev:web` - Start web development server
- `pnpm build` - Build all packages
- `pnpm test` - Run all tests
- `pnpm lint` - Lint all code
- `pnpm type-check` - TypeScript type checking

## 🔧 Configuration

### Environment Variables

- `NODE_ENV=development` - Enables mock DB mode for local development
- `PORT` - API server port (default: 3000)

### Mock DB Mode

For local development without Neo4j:

```bash
NODE_ENV=development pnpm dev:api
```

## 📚 Documentation

- [Contributing Guidelines](./CONTRIBUTING.md)
- [Security Policy](./SECURITY.md)
- [Changelog](./CHANGELOG.md)
- [Technical Specifications](./docs/tech-specs/)

## 🤝 Contributing

Please read [CONTRIBUTING.md](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.
