{"name": "workflow-mapper", "private": true, "packageManager": "pnpm@8.15.4", "scripts": {"dev:api": "pnpm --filter apps/api dev", "dev:web": "pnpm --filter apps/web dev", "build": "turbo run build --parallel", "lint": "eslint . --ext .ts,.tsx", "test": "turbo run test --parallel", "type-check": "turbo run type-check --parallel", "prepare": "husky install"}, "devDependencies": {"typescript": "5.4.3", "tsup": "8.0.2", "turbo": "1.13.2", "eslint": "8.56.0", "@typescript-eslint/eslint-plugin": "7.7.0", "@typescript-eslint/parser": "7.7.0", "eslint-plugin-prettier": "5.1.3", "eslint-config-prettier": "9.1.0", "prettier": "3.2.5", "jest": "29.7.0", "@types/jest": "29.5.12", "supertest": "7.1.1", "@types/supertest": "6.0.2", "vitest": "1.5.0", "@types/node": "20.12.7", "nodemon": "3.1.0", "husky": "9.0.11", "lint-staged": "15.2.2"}}